package model

import (
	"crypto/md5"
	"encoding/hex"
	"encoding/json"
	"fmt"
	"reflect"
	"strings"
	"time"

	"gorm.io/gorm"
)

// Question 题目表 - 优化版本（移除冗余字段）
type Question struct {
	// 核心字段
	ID           uint           `gorm:"primaryKey;comment:'问题ID'" json:"question_id"`
	CacheKey     string         `gorm:"size:128;uniqueIndex;not null;comment:'缓存键，基于题目内容生成'" json:"cache_key"`
	QuestionType string         `gorm:"size:20;index;not null;comment:'题目类型'" json:"question_type"`
	QuestionText string         `gorm:"type:text;not null;comment:'题目内容'" json:"question_text"`

	// 分散选项存储（便于单独更新和维护）
	OptionsA     string         `gorm:"type:text;comment:'选项A'" json:"options_a"`
	OptionsB     string         `gorm:"type:text;comment:'选项B'" json:"options_b"`
	OptionsC     string         `gorm:"type:text;comment:'选项C'" json:"options_c"`
	OptionsD     string         `gorm:"type:text;comment:'选项D'" json:"options_d"`
	OptionsY     string         `gorm:"type:text;comment:'选项Y（正确）'" json:"options_y"`
	OptionsN     string         `gorm:"type:text;comment:'选项N（错误）'" json:"options_n"`

	Answer       string         `gorm:"type:text;not null;comment:'正确答案'" json:"answer"`
	Analysis     string         `gorm:"type:text;comment:'答案解析'" json:"analysis"`

	// 统计字段
	Response     int            `gorm:"default:0;index;not null;comment:'响应次数'" json:"response"`

	// 图片字段
	QuestionImgRaw string       `gorm:"type:text;comment:'用户原始图片URL'" json:"question_img_raw"`

	// 元数据字段
	Subject      string         `gorm:"size:20;index;default:'未知';comment:'学科'" json:"subject"`
	Grade        string         `gorm:"size:20;default:'未知';comment:'年级'" json:"grade"`
	Difficulty   int            `gorm:"index;default:3;comment:'难度 1-5'" json:"difficulty"`
	SourceModel  string         `gorm:"size:50;default:'unknown';comment:'来源模型'" json:"source_model"`

	// 调试字段（可选）
	RawQwen      string         `gorm:"type:longtext;comment:'Qwen原始返回数据'" json:"raw_qwen,omitempty"`
	RawDeepseek  string         `gorm:"type:longtext;comment:'DeepSeek原始返回数据'" json:"raw_deepseek,omitempty"`

	// 时间字段
	CreatedAt    time.Time      `json:"created_at"`
	UpdatedAt    time.Time      `json:"updated_at"`
	DeletedAt    gorm.DeletedAt `gorm:"index" json:"-"`
}

// TableName 指定表名
func (Question) TableName() string {
	return "questions"
}

// QuestionDifficulty 题目难度常量
const (
	QuestionDifficultyVeryEasy = 1 // 非常简单
	QuestionDifficultyEasy     = 2 // 简单
	QuestionDifficultyMedium   = 3 // 中等
	QuestionDifficultyHard     = 4 // 困难
	QuestionDifficultyVeryHard = 5 // 非常困难
)

// QuestionType 题目类型常量
const (
	QuestionTypeSingleChoice = "单选题"
	QuestionTypeMultiChoice  = "多选题"
	QuestionTypeJudgment     = "判断题"
	QuestionTypeFillBlank    = "填空题"
	QuestionTypeEssay        = "解答题"
)

// 题目类型前缀（需要移除的）
var QuestionTypePrefixes = []string{
	"（判断题）", "（多选题）", "（单选题）",
	"(判断题)", "(多选题)", "(单选题)",
	"判断题：", "多选题：", "单选题：",
	"判断题", "多选题", "单选题",
}

// GenerateCacheKey 生成缓存键
func (q *Question) GenerateCacheKey() {
	// 基于题目类型、题目内容和选项生成缓存键
	content := q.QuestionType + ":" + q.QuestionText

	// 根据题目类型添加选项内容
	switch q.QuestionType {
	case QuestionTypeSingleChoice, QuestionTypeMultiChoice:
		content += "A:" + q.OptionsA + "B:" + q.OptionsB + "C:" + q.OptionsC + "D:" + q.OptionsD
	case QuestionTypeJudgment:
		content += "Y:" + q.OptionsY + "N:" + q.OptionsN
	}

	// 使用MD5生成哈希
	hasher := md5.New()
	hasher.Write([]byte(content))
	q.CacheKey = hex.EncodeToString(hasher.Sum(nil))
}

// GetOptions 获取选项（从分散字段转换为map）
func (q *Question) GetOptions() map[string]string {
	options := make(map[string]string)

	if q.OptionsA != "" {
		options["A"] = q.OptionsA
	}
	if q.OptionsB != "" {
		options["B"] = q.OptionsB
	}
	if q.OptionsC != "" {
		options["C"] = q.OptionsC
	}
	if q.OptionsD != "" {
		options["D"] = q.OptionsD
	}
	if q.OptionsY != "" {
		options["Y"] = q.OptionsY
	}
	if q.OptionsN != "" {
		options["N"] = q.OptionsN
	}

	return options
}

// 移除FromStructure方法 - 不再需要

// QuestionSearchRequest 拍照搜题请求
type QuestionSearchRequest struct {
	ImageURL string `json:"image_url" binding:"required,url" example:"https://example.com/image.jpg"`
}

// QuestionSearchResponse 拍照搜题响应（优化版本）
type QuestionSearchResponse struct {
	ID                 uint                     `json:"id"`
	Content            string                   `json:"content"`      // 兼容字段，使用question_text
	QuestionType       string                   `json:"question_type,omitempty"`
	QuestionText       string                   `json:"question_text,omitempty"`
	Options            map[string]string        `json:"options,omitempty"`
	Analysis           string                   `json:"analysis"`
	Answer             string                   `json:"answer"`
	Subject            string                   `json:"subject"`
	Grade              string                   `json:"grade"`
	Difficulty         int                      `json:"difficulty"`
	SourceModel        string                   `json:"source_model"`
	CacheHit           bool                     `json:"cache_hit"`    // 是否命中缓存
	ProcessTime        int64                    `json:"process_time"` // 处理时间（毫秒）
	QwenRawContent     string                   `json:"qwen_raw_content,omitempty"`     // Qwen原始content
	DeepseekRawContent string                   `json:"deepseek_raw_content,omitempty"` // DeepSeek原始content

	// 图片字段（移除question_img，保留question_img_raw）
	QuestionImgRaw     string                   `json:"question_img_raw,omitempty"`     // 用户原始图片
}

// QuestionAssociation 关联题目信息
type QuestionAssociation struct {
	ID           uint              `json:"id"`
	QuestionType string            `json:"question_type"`
	QuestionText string            `json:"question_text"`
	Options      map[string]string `json:"options,omitempty"`
	Answer       string            `json:"answer"`
	Analysis     string            `json:"analysis"`
}

// ToSearchResponse 转换为搜索响应格式
func (q *Question) ToSearchResponse(cacheHit bool, processTime int64) *QuestionSearchResponse {
	return &QuestionSearchResponse{
		ID:                  q.ID,
		Content:             q.QuestionText,
		QuestionType:        q.QuestionType,
		QuestionText:        q.QuestionText,
		Options:             q.GetOptions(),
		Analysis:            q.Analysis,
		Answer:              q.Answer,
		Subject:             q.Subject,
		Grade:               q.Grade,
		Difficulty:          q.Difficulty,
		SourceModel:         q.SourceModel,
		CacheHit:            cacheHit,
		ProcessTime:         processTime,
		QwenRawContent:      q.RawQwen,
		DeepseekRawContent:  q.RawDeepseek,
		QuestionImgRaw:      q.QuestionImgRaw,
	}
}

// ToAssociation 转换为关联题目格式
func (q *Question) ToAssociation() QuestionAssociation {
	options, _ := q.GetOptions() // 忽略错误，返回空map

	return QuestionAssociation{
		ID:           q.ID,
		QuestionType: q.QuestionType,
		QuestionText: q.QuestionText,
		Options:      options,
		Answer:       q.Answer,
		Analysis:     q.Analysis,
	}
}

// QuestionResponse 题目响应
type QuestionResponse struct {
	ID          uint      `json:"id"`
	Hash        string    `json:"hash"`
	Content     string    `json:"content"`
	Analysis    string    `json:"analysis"`
	Answer      string    `json:"answer"`
	Subject     string    `json:"subject"`
	Grade       string    `json:"grade"`
	Difficulty  int       `json:"difficulty"`
	SourceModel string    `json:"source_model"`
	CreatedAt   time.Time `json:"created_at"`
	UpdatedAt   time.Time `json:"updated_at"`
}

// ToResponse 转换为响应格式
func (q *Question) ToResponse() *QuestionResponse {
	return &QuestionResponse{
		ID:          q.ID,
		Hash:        q.CacheKey,
		Content:     q.QuestionText,
		Analysis:    q.Analysis,
		Answer:      q.Answer,
		Subject:     q.Subject,
		Grade:       q.Grade,
		Difficulty:  q.Difficulty,
		SourceModel: q.SourceModel,
		CreatedAt:   q.CreatedAt,
		UpdatedAt:   q.UpdatedAt,
	}
}

// QwenVLResponse Qwen-VL模型响应结构
type QwenVLResponse struct {
	Output struct {
		Choices []struct {
			Message struct {
				Content interface{} `json:"content"` // 可能是string或array
			} `json:"message"`
		} `json:"choices"`
	} `json:"output"`
	Usage struct {
		InputTokens  int `json:"input_tokens"`
		OutputTokens int `json:"output_tokens"`
		TotalTokens  int `json:"total_tokens"`
	} `json:"usage"`
}

// GetContentString 从Qwen-VL响应中提取内容字符串
func (r *QwenVLResponse) GetContentString() string {
	if len(r.Output.Choices) == 0 {
		fmt.Printf("❌ GetContentString: Choices数组为空\n")
		return ""
	}

	content := r.Output.Choices[0].Message.Content
	fmt.Printf("🔍 GetContentString: content类型 = %T\n", content)

	// 处理content可能是string或array的情况
	switch v := content.(type) {
	case string:
		// 直接返回字符串
		fmt.Printf("✅ GetContentString: 直接返回字符串内容\n")
		return v
	case []interface{}:
		// 如果是数组，查找text字段
		fmt.Printf("🔍 GetContentString: 处理数组格式，数组长度 = %d\n", len(v))
		for i, item := range v {
			fmt.Printf("🔍 GetContentString: 处理数组项 %d, 类型 = %T\n", i, item)
			if itemMap, ok := item.(map[string]interface{}); ok {
				fmt.Printf("🔍 GetContentString: 数组项 %d 是map，键: %v\n", i, getMapKeys(itemMap))
				if text, exists := itemMap["text"]; exists {
					if textStr, ok := text.(string); ok {
						fmt.Printf("✅ GetContentString: 找到text字段，内容长度 = %d\n", len(textStr))
						return textStr
					}
				}
			}
		}
		fmt.Printf("❌ GetContentString: 数组中未找到text字段\n")
		return ""
	default:
		fmt.Printf("❌ GetContentString: 未知的content类型 = %T\n", content)
		return ""
	}
}

// getMapKeys 获取map的所有键（用于调试）
func getMapKeys(m map[string]interface{}) []string {
	keys := make([]string, 0, len(m))
	for k := range m {
		keys = append(keys, k)
	}
	return keys
}

// DeepseekResponse Deepseek模型响应结构
type DeepseekResponse struct {
	Choices []struct {
		Message struct {
			Content string `json:"content"`
		} `json:"message"`
	} `json:"choices"`
	Usage struct {
		PromptTokens     int `json:"prompt_tokens"`
		CompletionTokens int `json:"completion_tokens"`
		TotalTokens      int `json:"total_tokens"`
	} `json:"usage"`
}

// 移除QuestionStructure - 不再需要，直接使用PreprocessedQuestion

// PreprocessedQuestion 预处理后的题目数据
type PreprocessedQuestion struct {
	QuestionType string            `json:"question_type"`
	QuestionText string            `json:"question_text"`
	OptionsA     string            `json:"options_a"`
	OptionsB     string            `json:"options_b"`
	OptionsC     string            `json:"options_c"`
	OptionsD     string            `json:"options_d"`
	OptionsY     string            `json:"options_y"`
	OptionsN     string            `json:"options_n"`
	Subject      string            `json:"subject"`
	Grade        string            `json:"grade"`
	Difficulty   int               `json:"difficulty"`
	RawContent   string            `json:"raw_content"`
	IsValid      bool              `json:"is_valid"`
	ErrorMessage string            `json:"error_message,omitempty"`
}

// QuestionManagementRequest 题库管理请求
type QuestionManagementRequest struct {
	QuestionType   string `json:"question_type" binding:"required"`
	QuestionText   string `json:"question_text" binding:"required"`
	QuestionImgRaw string `json:"question_img_raw"`
	OptionsA       string `json:"options_a"`
	OptionsB       string `json:"options_b"`
	OptionsC       string `json:"options_c"`
	OptionsD       string `json:"options_d"`
	OptionsY       string `json:"options_y"`
	OptionsN       string `json:"options_n"`
	Answer         string `json:"answer" binding:"required"`
	Analysis       string `json:"analysis"`
}

// QuestionUpdateRequest 题目更新请求
type QuestionUpdateRequest struct {
	QuestionType   *string `json:"question_type"`
	QuestionText   *string `json:"question_text"`
	QuestionImgRaw *string `json:"question_img_raw"`
	OptionsA       *string `json:"options_a"`
	OptionsB       *string `json:"options_b"`
	OptionsC       *string `json:"options_c"`
	OptionsD       *string `json:"options_d"`
	OptionsY       *string `json:"options_y"`
	OptionsN       *string `json:"options_n"`
	Answer         *string `json:"answer"`
	Analysis       *string `json:"analysis"`
}

// GenerateHashFromStructure 从结构化数据生成哈希
func GenerateHashFromStructure(structure *QuestionStructure) string {
	return GenerateSmartCacheKey(structure)
}

// GenerateSmartCacheKey 智能生成缓存键
func GenerateSmartCacheKey(structure *QuestionStructure) string {
	// 使用核心内容生成稳定的哈希
	content := structure.QuestionText
	if content == "" {
		content = structure.Content // 向后兼容
	}

	// 如果有选项，按键排序后拼接
	if len(structure.Options) > 0 {
		keys := make([]string, 0, len(structure.Options))
		for k := range structure.Options {
			keys = append(keys, k)
		}
		// 排序确保一致性
		for i := 0; i < len(keys); i++ {
			for j := i + 1; j < len(keys); j++ {
				if keys[i] > keys[j] {
					keys[i], keys[j] = keys[j], keys[i]
				}
			}
		}
		for _, k := range keys {
			content += k + structure.Options[k]
		}
	}

	// 使用SHA256生成更安全的哈希
	hasher := md5.New() // 暂时保持MD5兼容性
	hasher.Write([]byte(content))
	return hex.EncodeToString(hasher.Sum(nil))
}

// GetDifficultyName 获取难度名称
func GetDifficultyName(difficulty int) string {
	switch difficulty {
	case QuestionDifficultyVeryEasy:
		return "非常简单"
	case QuestionDifficultyEasy:
		return "简单"
	case QuestionDifficultyMedium:
		return "中等"
	case QuestionDifficultyHard:
		return "困难"
	case QuestionDifficultyVeryHard:
		return "非常困难"
	default:
		return "未知"
	}
}

// ValidateImageURL 验证图片URL
func ValidateImageURL(url string) error {
	if url == "" {
		return fmt.Errorf("图片URL不能为空")
	}
	
	// 这里可以添加更多的URL验证逻辑
	// 比如检查URL格式、文件扩展名等
	
	return nil
}

// CacheKey 生成缓存键
func GenerateCacheKey(hash string) string {
	return fmt.Sprintf("question:%s", hash)
}

// GenerateImageCacheKey 基于图片URL生成缓存键
func GenerateImageCacheKey(imageURL string) string {
	// 使用MD5生成图片URL的哈希值
	hasher := md5.New()
	hasher.Write([]byte(imageURL))
	hash := hex.EncodeToString(hasher.Sum(nil))
	return hash
}

// PhotoSearchRequest 拍照搜题请求结构（支持多种认证方式）
type PhotoSearchRequest struct {
	// 图片信息
	ImageURL string `json:"image_url" binding:"required"`

	// 认证方式1: 直接在请求体中（可选）
	AppKey    string `json:"app_key,omitempty"`
	SecretKey string `json:"secret_key,omitempty"`

	// 认证方式2: 嵌套在auth对象中（可选）
	Auth *AuthInfo `json:"auth,omitempty"`

	// 认证方式3: 签名认证（可选，高安全性）
	Timestamp int64  `json:"timestamp,omitempty"`
	Signature string `json:"signature,omitempty"`
}

// AuthInfo 认证信息结构
type AuthInfo struct {
	AppKey    string `json:"app_key"`
	SecretKey string `json:"secret_key"`
}

// GetAuthCredentials 获取认证凭据
func (r *PhotoSearchRequest) GetAuthCredentials() (string, string) {
	// 优先使用直接字段
	if r.AppKey != "" && r.SecretKey != "" {
		return r.AppKey, r.SecretKey
	}

	// 其次使用auth对象
	if r.Auth != nil && r.Auth.AppKey != "" && r.Auth.SecretKey != "" {
		return r.Auth.AppKey, r.Auth.SecretKey
	}

	return "", ""
}

// HasSignatureAuth 检查是否包含签名认证信息
func (r *PhotoSearchRequest) HasSignatureAuth() bool {
	return r.AppKey != "" && r.Timestamp > 0 && r.Signature != ""
}

// Validate 验证请求参数
func (r *PhotoSearchRequest) Validate() error {
	if r.ImageURL == "" {
		return fmt.Errorf("图片URL不能为空")
	}

	// 认证信息已经在中间件中验证，这里不需要再检查
	// 只验证图片URL格式
	return ValidateImageURL(r.ImageURL)
}

// PreprocessQwenResult 预处理Qwen返回的数据
func PreprocessQwenResult(structure *QuestionStructure) (*PreprocessedQuestion, error) {
	fmt.Printf("🔍 开始预处理Qwen数据\n")
	fmt.Printf("🔍 原始题目类型: %s\n", structure.QuestionType)
	fmt.Printf("🔍 原始题目内容: %s\n", structure.QuestionText)

	result := &PreprocessedQuestion{
		QuestionType: structure.QuestionType, // 题目类型保持不变，不处理
		QuestionText: structure.QuestionText,
		Subject:      structure.Subject,
		Grade:        structure.Grade,
		Difficulty:   structure.Difficulty,
		RawContent:   structure.RawContent,
		IsValid:      true,
	}

	// 1. 检查题目内容是否为空
	if strings.TrimSpace(result.QuestionText) == "" {
		fmt.Printf("❌ 题目内容为空\n")
		result.IsValid = false
		result.ErrorMessage = "图片不标准，请重新拍摄"
		return result, fmt.Errorf("题目内容为空")
	}

	// 2. 移除题目内容中可能存在的题目类型前缀（由于视觉模型不稳定导致）
	originalText := result.QuestionText
	result.QuestionText = removeQuestionTypePrefixFromContent(result.QuestionText)
	fmt.Printf("🔍 前缀移除前: %s\n", originalText)
	fmt.Printf("🔍 前缀移除后: %s\n", result.QuestionText)

	// 3. 处理选项
	fmt.Printf("🔍 开始处理选项，题目类型: %s\n", result.QuestionType)
	if err := processOptions(result, structure.Options); err != nil {
		fmt.Printf("❌ 选项处理失败: %v\n", err)
		result.IsValid = false
		result.ErrorMessage = "图片不标准，请重新拍摄"
		return result, err
	}

	fmt.Printf("✅ 预处理完成，结果有效: %v\n", result.IsValid)
	return result, nil
}

// removeQuestionTypePrefixFromContent 移除题目内容中可能存在的题目类型前缀
// 注意：这里只处理题目内容，不处理题目类型字段
func removeQuestionTypePrefixFromContent(questionText string) string {
	text := strings.TrimSpace(questionText)

	// 1. 移除数字序号前缀（如"20、"、"1."、"(1)"等）
	text = removeNumberPrefix(text)

	// 2. 遍历所有可能的题目类型前缀，移除题目内容中的这些前缀
	for _, prefix := range QuestionTypePrefixes {
		if strings.HasPrefix(text, prefix) {
			text = strings.TrimSpace(strings.TrimPrefix(text, prefix))
			break // 只移除第一个匹配的前缀
		}
	}

	return text
}

// removeNumberPrefix 移除数字序号前缀
func removeNumberPrefix(text string) string {
	text = strings.TrimSpace(text)

	// 简单的字符串匹配方式（不使用正则表达式）
	for i := 0; i < len(text); i++ {
		char := text[i]

		// 检查是否以数字开头
		if char >= '0' && char <= '9' {
			// 找到数字后面的分隔符
			for j := i + 1; j < len(text); j++ {
				nextChar := text[j]
				// 检查顿号（、）- 使用字符串比较
				if string(nextChar) == "、" || nextChar == '.' || nextChar == ')' {
					// 找到分隔符，移除前缀
					return strings.TrimSpace(text[j+1:])
				} else if nextChar < '0' || nextChar > '9' {
					// 不是数字也不是分隔符，停止查找
					break
				}
			}
		} else if char == '(' {
			// 检查括号数字格式 (1)
			for j := i + 1; j < len(text); j++ {
				nextChar := text[j]
				if nextChar == ')' {
					// 找到右括号，移除前缀
					return strings.TrimSpace(text[j+1:])
				} else if nextChar < '0' || nextChar > '9' {
					// 不是数字，停止查找
					break
				}
			}
		}

		// 如果第一个字符不是数字或括号，直接返回
		break
	}

	// 特殊处理中文顿号
	if strings.Contains(text, "、") {
		parts := strings.Split(text, "、")
		if len(parts) > 1 {
			// 检查第一部分是否为数字
			firstPart := strings.TrimSpace(parts[0])
			if isNumeric(firstPart) {
				return strings.TrimSpace(strings.Join(parts[1:], "、"))
			}
		}
	}

	return text
}

// isNumeric 检查字符串是否为数字
func isNumeric(s string) bool {
	if s == "" {
		return false
	}
	for _, char := range s {
		if char < '0' || char > '9' {
			return false
		}
	}
	return true
}

// processOptions 处理选项
func processOptions(result *PreprocessedQuestion, options map[string]string) error {
	// 根据题目类型处理选项
	switch result.QuestionType {
	case QuestionTypeSingleChoice, QuestionTypeMultiChoice:
		// 单选题和多选题需要检查ABCD选项
		if err := validateChoiceOptions(options); err != nil {
			return err
		}

		// 设置选项
		result.OptionsA = options["A"]
		result.OptionsB = options["B"]
		result.OptionsC = options["C"]
		result.OptionsD = options["D"]

		// 清空YN选项
		result.OptionsY = ""
		result.OptionsN = ""

	case QuestionTypeJudgment:
		// 判断题保持原始选项格式，从Qwen识别结果中提取
		if options["Y"] != "" {
			result.OptionsY = options["Y"]
		}
		if options["N"] != "" {
			result.OptionsN = options["N"]
		}

		// 如果DeepSeek返回了A/B格式，也支持
		if options["A"] != "" {
			result.OptionsA = options["A"]
		}
		if options["B"] != "" {
			result.OptionsB = options["B"]
		}

		// 清空其他选项
		result.OptionsC = ""
		result.OptionsD = ""

	default:
		// 其他题型（填空题、解答题等）清空所有选项
		result.OptionsA = ""
		result.OptionsB = ""
		result.OptionsC = ""
		result.OptionsD = ""
		result.OptionsY = ""
		result.OptionsN = ""
	}

	return nil
}

// validateChoiceOptions 验证选择题选项
func validateChoiceOptions(options map[string]string) error {
	requiredOptions := []string{"A", "B", "C", "D"}

	for _, option := range requiredOptions {
		if value, exists := options[option]; !exists || strings.TrimSpace(value) == "" {
			return fmt.Errorf("选项%s为空", option)
		}
	}

	return nil
}

// GenerateCacheKeyFromPreprocessed 从预处理数据生成缓存键
func GenerateCacheKeyFromPreprocessed(preprocessed *PreprocessedQuestion) string {
	// 基于题目类型、题目内容和选项生成缓存键
	content := preprocessed.QuestionType + ":" + preprocessed.QuestionText

	// 根据题目类型添加选项内容
	switch preprocessed.QuestionType {
	case QuestionTypeSingleChoice, QuestionTypeMultiChoice:
		content += "A:" + preprocessed.OptionsA
		content += "B:" + preprocessed.OptionsB
		content += "C:" + preprocessed.OptionsC
		content += "D:" + preprocessed.OptionsD
	case QuestionTypeJudgment:
		content += "Y:" + preprocessed.OptionsY
		content += "N:" + preprocessed.OptionsN
	}

	// 使用MD5生成哈希
	hasher := md5.New()
	hasher.Write([]byte(content))
	return hex.EncodeToString(hasher.Sum(nil))
}

// FromPreprocessed 从预处理数据创建Question
func (q *Question) FromPreprocessed(preprocessed *PreprocessedQuestion, imageURL, rawQwen, rawDeepseek, analysis, answer string) {
	// 设置新架构字段
	q.CacheKey = GenerateCacheKeyFromPreprocessed(preprocessed)
	q.QuestionType = preprocessed.QuestionType
	q.QuestionDoc = preprocessed.QuestionText
	q.QuestionImg = "" // 题目图片暂时为空
	q.QuestionImgRaw = imageURL
	q.OptionsA = preprocessed.OptionsA
	q.OptionsB = preprocessed.OptionsB
	q.OptionsC = preprocessed.OptionsC
	q.OptionsD = preprocessed.OptionsD
	q.OptionsY = preprocessed.OptionsY
	q.OptionsN = preprocessed.OptionsN
	q.Answer = answer
	q.Analysis = analysis
	q.Response = 0
	q.RawQwen = rawQwen
	q.RawDeepseek = rawDeepseek
	q.Associates = ""

	// 设置兼容字段
	q.Hash = q.CacheKey
	q.Content = preprocessed.QuestionText
	q.QuestionText = preprocessed.QuestionText
	q.Subject = preprocessed.Subject
	q.Grade = preprocessed.Grade
	q.Difficulty = preprocessed.Difficulty
	q.RawContent = preprocessed.RawContent

	// 设置兼容选项
	options := make(map[string]string)
	if preprocessed.OptionsA != "" {
		options["A"] = preprocessed.OptionsA
	}
	if preprocessed.OptionsB != "" {
		options["B"] = preprocessed.OptionsB
	}
	if preprocessed.OptionsC != "" {
		options["C"] = preprocessed.OptionsC
	}
	if preprocessed.OptionsD != "" {
		options["D"] = preprocessed.OptionsD
	}
	if preprocessed.OptionsY != "" {
		options["Y"] = preprocessed.OptionsY
	}
	if preprocessed.OptionsN != "" {
		options["N"] = preprocessed.OptionsN
	}
	q.SetOptions(options)
}

// FromPreprocessedWithDeepSeek 从预处理数据和DeepSeek结果创建Question
func (q *Question) FromPreprocessedWithDeepSeek(preprocessed *PreprocessedQuestion, imageURL, rawQwen string, deepseekResult interface{}) {
	// 核心字段
	q.CacheKey = GenerateCacheKeyFromPreprocessed(preprocessed)
	q.QuestionType = preprocessed.QuestionType
	q.QuestionText = preprocessed.QuestionText
	q.QuestionImgRaw = imageURL
	q.Response = 0
	q.RawQwen = rawQwen
	q.SourceModel = "qwen-vl-plus,deepseek-chat"

	// 选项字段
	q.OptionsA = preprocessed.OptionsA
	q.OptionsB = preprocessed.OptionsB
	q.OptionsC = preprocessed.OptionsC
	q.OptionsD = preprocessed.OptionsD
	q.OptionsY = preprocessed.OptionsY
	q.OptionsN = preprocessed.OptionsN

	// 元数据字段
	q.Subject = preprocessed.Subject
	q.Grade = preprocessed.Grade
	q.Difficulty = preprocessed.Difficulty

	// 处理DeepSeek结果
	if deepseekResult != nil {
		q.Analysis = extractStringField(deepseekResult, "Analysis")
		q.Answer = extractStringField(deepseekResult, "Answer")
		q.RawDeepseek = extractStringField(deepseekResult, "RawContent")

		// 如果DeepSeek返回了更好的选项，使用DeepSeek的选项
		deepseekOptions := extractMapField(deepseekResult, "Options")
		for key, value := range deepseekOptions {
			if value == "" {
				continue
			}
			switch key {
			case "A":
				q.OptionsA = value
			case "B":
				q.OptionsB = value
			case "C":
				q.OptionsC = value
			case "D":
				q.OptionsD = value
			case "Y":
				q.OptionsY = value
			case "N":
				q.OptionsN = value
			}
		}
	}
}

// 移除冗余的辅助方法

// extractStringField 从interface{}中提取字符串字段
func extractStringField(obj interface{}, fieldName string) string {
	if obj == nil {
		return ""
	}

	v := reflect.ValueOf(obj)
	if v.Kind() == reflect.Ptr {
		v = v.Elem()
	}

	if v.Kind() != reflect.Struct {
		return ""
	}

	field := v.FieldByName(fieldName)
	if !field.IsValid() || field.Kind() != reflect.String {
		return ""
	}

	return field.String()
}

// extractMapField 从interface{}中提取map字段
func extractMapField(obj interface{}, fieldName string) map[string]string {
	if obj == nil {
		return make(map[string]string)
	}

	v := reflect.ValueOf(obj)
	if v.Kind() == reflect.Ptr {
		v = v.Elem()
	}

	if v.Kind() != reflect.Struct {
		return make(map[string]string)
	}

	field := v.FieldByName(fieldName)
	if !field.IsValid() || field.Kind() != reflect.Map {
		return make(map[string]string)
	}

	result := make(map[string]string)
	for _, key := range field.MapKeys() {
		if keyStr, ok := key.Interface().(string); ok {
			value := field.MapIndex(key)
			if valueStr, ok := value.Interface().(string); ok {
				result[keyStr] = valueStr
			}
		}
	}

	return result
}

// ToManagementResponse 转换为管理响应格式
func (q *Question) ToManagementResponse() map[string]interface{} {
	return map[string]interface{}{
		"question_id":      q.ID,
		"cache_key":        q.CacheKey,
		"question_type":    q.QuestionType,
		"question_text":    q.QuestionText,
		"question_img_raw": q.QuestionImgRaw,
		"options_a":        q.OptionsA,
		"options_b":        q.OptionsB,
		"options_c":        q.OptionsC,
		"options_d":        q.OptionsD,
		"options_y":        q.OptionsY,
		"options_n":        q.OptionsN,
		"answer":           q.Answer,
		"analysis":         q.Analysis,
		"response":         q.Response,
		"subject":          q.Subject,
		"grade":            q.Grade,
		"difficulty":       q.Difficulty,
		"source_model":     q.SourceModel,
		"raw_qwen":         q.RawQwen,
		"raw_deepseek":     q.RawDeepseek,
		"created_at":       q.CreatedAt,
		"updated_at":       q.UpdatedAt,
	}
}
