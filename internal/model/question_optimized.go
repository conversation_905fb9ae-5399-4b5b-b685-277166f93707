package model

import (
	"crypto/md5"
	"encoding/hex"
	"encoding/json"
	"fmt"
	"strings"
	"time"

	"gorm.io/gorm"
)

// QuestionOptimized 优化后的题目表结构 - 移除冗余字段
type QuestionOptimized struct {
	// 核心字段
	ID           uint           `gorm:"primaryKey;comment:'问题ID'" json:"id"`
	CacheKey     string         `gorm:"size:128;uniqueIndex;comment:'缓存键，基于题目内容生成'" json:"cache_key"`
	QuestionType string         `gorm:"size:20;index;comment:'题目类型'" json:"question_type"`
	QuestionDoc  string         `gorm:"type:text;comment:'题目内容'" json:"question_doc"`
	Options      string         `gorm:"type:text;comment:'选项JSON格式'" json:"options"`
	Answer       string         `gorm:"type:text;comment:'正确答案'" json:"answer"`
	Analysis     string         `gorm:"type:text;comment:'答案解析'" json:"analysis"`
	
	// 统计字段
	Response     int            `gorm:"default:0;index;comment:'响应次数'" json:"response"`
	
	// 图片字段（可选）
	QuestionImgRaw string       `gorm:"type:text;comment:'用户原始图片URL'" json:"question_img_raw,omitempty"`
	
	// 元数据字段
	Subject      string         `gorm:"size:20;index;comment:'学科'" json:"subject"`
	Grade        string         `gorm:"size:20;comment:'年级'" json:"grade"`
	Difficulty   int            `gorm:"index;comment:'难度 1-5'" json:"difficulty"`
	SourceModel  string         `gorm:"size:50;comment:'来源模型'" json:"source_model"`
	
	// 调试字段（可选保留）
	RawQwen      string         `gorm:"type:longtext;comment:'Qwen原始返回数据'" json:"raw_qwen,omitempty"`
	RawDeepseek  string         `gorm:"type:longtext;comment:'DeepSeek原始返回数据'" json:"raw_deepseek,omitempty"`
	
	// 时间字段
	CreatedAt    time.Time      `json:"created_at"`
	UpdatedAt    time.Time      `json:"updated_at"`
	DeletedAt    gorm.DeletedAt `gorm:"index" json:"-"`
}

// TableName 指定表名
func (QuestionOptimized) TableName() string {
	return "questions_optimized"
}

// OptionMap 选项映射类型
type OptionMap map[string]string

// SetOptions 设置选项（将map转换为JSON字符串存储）
func (q *QuestionOptimized) SetOptions(options OptionMap) error {
	if len(options) == 0 {
		q.Options = ""
		return nil
	}
	data, err := json.Marshal(options)
	if err != nil {
		return err
	}
	q.Options = string(data)
	return nil
}

// GetOptions 获取选项（将JSON字符串转换为map）
func (q *QuestionOptimized) GetOptions() (OptionMap, error) {
	if q.Options == "" {
		return make(OptionMap), nil
	}
	var options OptionMap
	err := json.Unmarshal([]byte(q.Options), &options)
	if err != nil {
		return nil, err
	}
	return options, nil
}

// GenerateCacheKey 生成缓存键
func (q *QuestionOptimized) GenerateCacheKey() {
	// 基于题目类型、题目内容和选项生成缓存键
	content := q.QuestionType + ":" + q.QuestionDoc
	
	// 添加选项内容（如果有）
	if q.Options != "" {
		content += ":" + q.Options
	}
	
	// 使用MD5生成哈希
	hasher := md5.New()
	hasher.Write([]byte(content))
	q.CacheKey = hex.EncodeToString(hasher.Sum(nil))
}

// FromPreprocessed 从预处理数据创建优化后的Question
func (q *QuestionOptimized) FromPreprocessed(preprocessed *PreprocessedQuestion, imageURL, rawQwen string, deepseekResult interface{}) {
	// 设置基本字段
	q.QuestionType = preprocessed.QuestionType
	q.QuestionDoc = preprocessed.QuestionText
	q.QuestionImgRaw = imageURL
	q.Response = 0
	q.RawQwen = rawQwen
	q.Subject = preprocessed.Subject
	q.Grade = preprocessed.Grade
	q.Difficulty = preprocessed.Difficulty
	
	// 处理选项
	options := make(OptionMap)
	switch preprocessed.QuestionType {
	case QuestionTypeSingleChoice, QuestionTypeMultiChoice:
		if preprocessed.OptionsA != "" {
			options["A"] = preprocessed.OptionsA
		}
		if preprocessed.OptionsB != "" {
			options["B"] = preprocessed.OptionsB
		}
		if preprocessed.OptionsC != "" {
			options["C"] = preprocessed.OptionsC
		}
		if preprocessed.OptionsD != "" {
			options["D"] = preprocessed.OptionsD
		}
	case QuestionTypeJudgment:
		if preprocessed.OptionsY != "" {
			options["Y"] = preprocessed.OptionsY
		}
		if preprocessed.OptionsN != "" {
			options["N"] = preprocessed.OptionsN
		}
	}
	q.SetOptions(options)
	
	// 处理DeepSeek结果
	if deepseekResult != nil {
		q.Analysis = extractStringField(deepseekResult, "Analysis")
		q.Answer = extractStringField(deepseekResult, "Answer")
		q.RawDeepseek = extractStringField(deepseekResult, "RawContent")
		
		// 如果DeepSeek返回了更好的选项，使用DeepSeek的选项
		deepseekOptions := extractMapField(deepseekResult, "Options")
		if len(deepseekOptions) > 0 {
			q.SetOptions(deepseekOptions)
		}
	}
	
	// 生成缓存键
	q.GenerateCacheKey()
	q.SourceModel = "qwen-vl-plus,deepseek-chat"
}

// ToSearchResponse 转换为搜索响应格式
func (q *QuestionOptimized) ToSearchResponse(cacheHit bool, processTime int64) *QuestionSearchResponseOptimized {
	options, _ := q.GetOptions() // 忽略错误，返回空map
	
	return &QuestionSearchResponseOptimized{
		ID:                  q.ID,
		QuestionType:        q.QuestionType,
		QuestionDoc:         q.QuestionDoc,
		Options:             options,
		Analysis:            q.Analysis,
		Answer:              q.Answer,
		Subject:             q.Subject,
		Grade:               q.Grade,
		Difficulty:          q.Difficulty,
		SourceModel:         q.SourceModel,
		CacheHit:            cacheHit,
		ProcessTime:         processTime,
		QuestionImgRaw:      q.QuestionImgRaw,
		QwenRawContent:      q.RawQwen,
		DeepseekRawContent:  q.RawDeepseek,
	}
}

// QuestionSearchResponseOptimized 优化后的搜索响应
type QuestionSearchResponseOptimized struct {
	ID                 uint                     `json:"id"`
	QuestionType       string                   `json:"question_type"`
	QuestionDoc        string                   `json:"question_doc"`
	Options            OptionMap                `json:"options,omitempty"`
	Analysis           string                   `json:"analysis"`
	Answer             string                   `json:"answer"`
	Subject            string                   `json:"subject"`
	Grade              string                   `json:"grade"`
	Difficulty         int                      `json:"difficulty"`
	SourceModel        string                   `json:"source_model"`
	CacheHit           bool                     `json:"cache_hit"`
	ProcessTime        int64                    `json:"process_time"`
	QuestionImgRaw     string                   `json:"question_img_raw,omitempty"`
	QwenRawContent     string                   `json:"qwen_raw_content,omitempty"`
	DeepseekRawContent string                   `json:"deepseek_raw_content,omitempty"`
}

// QuestionManagementRequestOptimized 优化后的题库管理请求
type QuestionManagementRequestOptimized struct {
	QuestionType   string    `json:"question_type" binding:"required"`
	QuestionDoc    string    `json:"question_doc" binding:"required"`
	Options        OptionMap `json:"options"`
	Answer         string    `json:"answer" binding:"required"`
	Analysis       string    `json:"analysis"`
	QuestionImgRaw string    `json:"question_img_raw"`
	Subject        string    `json:"subject"`
	Grade          string    `json:"grade"`
	Difficulty     int       `json:"difficulty"`
}

// CreateFromManagementRequest 从管理请求创建题目
func (q *QuestionOptimized) CreateFromManagementRequest(req *QuestionManagementRequestOptimized) {
	q.QuestionType = req.QuestionType
	q.QuestionDoc = req.QuestionDoc
	q.Answer = req.Answer
	q.Analysis = req.Analysis
	q.QuestionImgRaw = req.QuestionImgRaw
	q.Subject = req.Subject
	q.Grade = req.Grade
	q.Difficulty = req.Difficulty
	q.Response = 0
	q.SourceModel = "manual"
	
	// 设置选项
	q.SetOptions(req.Options)
	
	// 生成缓存键
	q.GenerateCacheKey()
}

// ValidateQuestionType 验证题目类型
func ValidateQuestionType(questionType string) bool {
	validTypes := []string{
		QuestionTypeSingleChoice,
		QuestionTypeMultiChoice,
		QuestionTypeJudgment,
		QuestionTypeFillBlank,
		QuestionTypeEssay,
	}
	
	for _, validType := range validTypes {
		if questionType == validType {
			return true
		}
	}
	return false
}

// ValidateOptions 验证选项
func ValidateOptions(questionType string, options OptionMap) error {
	switch questionType {
	case QuestionTypeSingleChoice, QuestionTypeMultiChoice:
		// 选择题必须有A、B选项，C、D可选
		if options["A"] == "" || options["B"] == "" {
			return fmt.Errorf("选择题必须包含A、B选项")
		}
	case QuestionTypeJudgment:
		// 判断题必须有Y、N选项
		if options["Y"] == "" || options["N"] == "" {
			return fmt.Errorf("判断题必须包含Y、N选项")
		}
	case QuestionTypeFillBlank, QuestionTypeEssay:
		// 填空题和解答题不需要选项
		if len(options) > 0 {
			return fmt.Errorf("填空题和解答题不应包含选项")
		}
	}
	return nil
}
