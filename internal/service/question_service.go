package service

import (
	"fmt"
	"solve_api/internal/model"
	"solve_api/internal/repository"
	"strings"
	"time"
)

type QuestionService struct {
	questionRepo      *repository.QuestionRepository
	questionCacheRepo *repository.QuestionCacheRepository
	aiService         *AIService
	userRepo          *repository.UserRepository
	balanceLogRepo    *repository.BalanceLogRepository
}

// NewQuestionService 创建题目服务实例
func NewQuestionService(
	questionRepo *repository.QuestionRepository,
	questionCacheRepo *repository.QuestionCacheRepository,
	aiService *AIService,
	userRepo *repository.UserRepository,
	balanceLogRepo *repository.BalanceLogRepository,
) *QuestionService {
	return &QuestionService{
		questionRepo:      questionRepo,
		questionCacheRepo: questionCacheRepo,
		aiService:         aiService,
		userRepo:          userRepo,
		balanceLogRepo:    balanceLogRepo,
	}
}

// Search 拍照搜题主要业务逻辑 - 重构版本支持S1需求
func (s *QuestionService) Search(userID uint, req *model.QuestionSearchRequest) (*model.QuestionSearchResponse, error) {
	startTime := time.Now()
	fmt.Printf("🚀 [性能分析] 开始搜题流程 - 用户ID: %d, 图片URL: %s\n", userID, req.ImageURL)

	// 1. 验证图片URL
	step1Start := time.Now()
	if err := s.aiService.ValidateImageURL(req.ImageURL); err != nil {
		return nil, fmt.Errorf("图片URL验证失败: %w", err)
	}
	step1Duration := time.Since(step1Start)
	fmt.Printf("⏱️ [性能分析] 步骤1-图片URL验证完成: %v\n", step1Duration)

	// 2. 调用Qwen-VL获取题目结构
	step2Start := time.Now()
	fmt.Printf("🎯 [性能分析] 步骤2-开始调用Qwen-VL进行图像识别\n")
	qwenResult, err := s.aiService.CallQwenVL(req.ImageURL)
	if err != nil {
		return nil, fmt.Errorf("图像识别失败: %w", err)
	}
	step2Duration := time.Since(step2Start)
	fmt.Printf("✅ [性能分析] 步骤2-Qwen-VL调用完成: %v\n", step2Duration)

	// 3. 预处理Qwen返回的数据
	step3Start := time.Now()
	fmt.Printf("🔄 [性能分析] 步骤3-开始预处理Qwen数据\n")
	preprocessed, err := model.PreprocessQwenResult(qwenResult.Structure)
	if err != nil || !preprocessed.IsValid {
		return nil, fmt.Errorf(preprocessed.ErrorMessage)
	}
	step3Duration := time.Since(step3Start)
	fmt.Printf("✅ [性能分析] 步骤3-Qwen数据预处理完成: %v\n", step3Duration)

	// 4. 基于预处理后的内容生成缓存键
	step4Start := time.Now()
	cacheKey := model.GenerateCacheKeyFromPreprocessed(preprocessed)
	step4Duration := time.Since(step4Start)
	fmt.Printf("🔑 [性能分析] 步骤4-缓存键生成完成: %v, 缓存键: %s\n", step4Duration, cacheKey)

	// 5. 查找Redis缓存
	step5Start := time.Now()
	fmt.Printf("🔍 [性能分析] 步骤5-开始查找Redis缓存\n")
	questions, err := s.questionCacheRepo.GetWithAssociates(cacheKey)
	step5Duration := time.Since(step5Start)
	if err == nil && len(questions) > 0 {
		// Redis命中，增加响应次数并返回
		fmt.Printf("🎯 [性能分析] 步骤5-Redis缓存命中: %v, 题目数量: %d\n", step5Duration, len(questions))

		incrementStart := time.Now()
		s.questionRepo.IncrementResponse(questions[0].ID)
		incrementDuration := time.Since(incrementStart)
		fmt.Printf("📊 [性能分析] 响应次数更新完成: %v\n", incrementDuration)

		processTime := time.Since(startTime).Milliseconds()
		fmt.Printf("🏁 [性能分析] Redis缓存命中流程总耗时: %v\n", time.Since(startTime))

		// 如果有关联题目，返回所有相关题目
		if len(questions) > 1 {
			return s.buildAssociatedResponse(questions, true, processTime), nil
		}
		return questions[0].ToSearchResponse(true, processTime), nil
	}
	fmt.Printf("❌ [性能分析] 步骤5-Redis缓存未命中: %v, 错误: %v\n", step5Duration, err)

	// 6. Redis未命中，降级查找MySQL
	step6Start := time.Now()
	fmt.Printf("🗄️ [业务性能分析] 步骤6-开始查找MySQL数据库\n")
	questions, err = s.questionRepo.GetWithAssociates(cacheKey)
	step6Duration := time.Since(step6Start)
	if err == nil && len(questions) > 0 {
		// MySQL命中，回传Redis并返回
		fmt.Printf("🎯 [业务性能分析] 步骤6-MySQL数据库命中: %v, 题目数量: %d\n", step6Duration, len(questions))

		// 回传Redis
		cacheBackStart := time.Now()
		fmt.Printf("🔄 [业务性能分析] 开始回传数据到Redis\n")
		for _, q := range questions {
			s.questionCacheRepo.Set(q.CacheKey, q)
		}
		cacheBackDuration := time.Since(cacheBackStart)
		fmt.Printf("✅ [业务性能分析] Redis回传完成: %v\n", cacheBackDuration)

		incrementStart := time.Now()
		s.questionRepo.IncrementResponse(questions[0].ID)
		incrementDuration := time.Since(incrementStart)
		fmt.Printf("📊 [业务性能分析] 响应次数更新完成: %v\n", incrementDuration)

		processTime := time.Since(startTime).Milliseconds()
		fmt.Printf("🏁 [业务性能分析] MySQL命中流程总耗时: %v\n", time.Since(startTime))

		if len(questions) > 1 {
			return s.buildAssociatedResponse(questions, true, processTime), nil
		}
		return questions[0].ToSearchResponse(true, processTime), nil
	}
	fmt.Printf("❌ [业务性能分析] 步骤6-MySQL数据库未命中: %v, 错误: %v\n", step6Duration, err)

	// 7. MySQL也未命中，降级提交DeepSeek
	step7Start := time.Now()
	fmt.Printf("🧠 [业务性能分析] 步骤7-开始调用DeepSeek进行题目解析\n")
	deepseekResult, err := s.aiService.CallDeepseekWithLogID(qwenResult, qwenResult.LogID)
	if err != nil {
		return nil, fmt.Errorf("题目解析失败: %w", err)
	}
	step7Duration := time.Since(step7Start)
	fmt.Printf("✅ [业务性能分析] 步骤7-DeepSeek调用完成: %v\n", step7Duration)

	// 8. 创建新题目记录
	step8Start := time.Now()
	fmt.Printf("📝 [业务性能分析] 步骤8-开始创建新题目记录\n")
	question := &model.Question{
		SourceModel: "qwen-vl-plus,deepseek-chat",
	}

	// 使用新的FromPreprocessedWithDeepSeek方法（支持S1需求）
	question.FromPreprocessedWithDeepSeek(preprocessed, req.ImageURL, qwenResult.RawContent, deepseekResult)
	step8Duration := time.Since(step8Start)
	fmt.Printf("✅ [业务性能分析] 步骤8-题目记录创建完成: %v\n", step8Duration)

	// 9. 保存到题库表
	step9Start := time.Now()
	fmt.Printf("💾 [业务性能分析] 步骤9-开始保存到MySQL数据库\n")
	if err := s.questionRepo.Create(question); err != nil {
		return nil, fmt.Errorf("保存题目失败: %w", err)
	}
	step9Duration := time.Since(step9Start)
	fmt.Printf("✅ [业务性能分析] 步骤9-MySQL数据库保存完成: %v\n", step9Duration)

	// 10. 保存到Redis缓存
	step10Start := time.Now()
	fmt.Printf("🔄 [业务性能分析] 步骤10-开始保存到Redis缓存\n")
	if err := s.questionCacheRepo.Set(question.CacheKey, question); err != nil {
		// 缓存失败不影响主流程
		fmt.Printf("⚠️ 缓存保存失败: %v\n", err)
	}
	step10Duration := time.Since(step10Start)
	fmt.Printf("✅ [业务性能分析] 步骤10-Redis缓存保存完成: %v\n", step10Duration)

	// 11. 扣费处理
	step11Start := time.Now()
	fmt.Printf("💰 [业务性能分析] 步骤11-开始扣费处理\n")
	if err := s.processPayment(userID, model.ApplicationTypePhotoSearch); err != nil {
		// 扣费失败不影响返回结果，但记录日志
		fmt.Printf("⚠️ 扣费失败: %v\n", err)
	}
	step11Duration := time.Since(step11Start)
	fmt.Printf("✅ [业务性能分析] 步骤11-扣费处理完成: %v\n", step11Duration)

	// 12. 构建响应
	step12Start := time.Now()
	fmt.Printf("📦 [业务性能分析] 步骤12-开始构建响应数据\n")
	processTime := time.Since(startTime).Milliseconds()
	response := question.ToSearchResponse(false, processTime)

	// 设置原始content数据
	response.QwenRawContent = qwenResult.RawContent
	response.DeepseekRawContent = deepseekResult.RawContent
	step12Duration := time.Since(step12Start)
	fmt.Printf("✅ [业务性能分析] 步骤12-响应数据构建完成: %v\n", step12Duration)

	totalDuration := time.Since(startTime)
	fmt.Printf("🏁 [业务性能分析] 完整搜题流程总耗时: %v\n", totalDuration)

	return response, nil
}

// processPayment 处理扣费
func (s *QuestionService) processPayment(userID uint, serviceType int) error {
	// 获取用户信息
	user, err := s.userRepo.GetByID(userID)
	if err != nil {
		return fmt.Errorf("查询用户信息失败: %w", err)
	}
	if user == nil {
		return fmt.Errorf("用户不存在")
	}

	// 获取服务价格（这里简化处理，使用固定价格）
	price := 0.01 // 默认0.01元/次

	// 检查余额
	if user.Balance < price {
		return fmt.Errorf("余额不足")
	}

	// 扣费
	user.Balance -= price
	if err := s.userRepo.Update(user); err != nil {
		return fmt.Errorf("扣费失败: %w", err)
	}

	// 记录余额变动日志
	balanceLog := &model.BalanceLog{
		UserID:      userID,
		Amount:      -price,
		Balance:     user.Balance,
		Type:        model.BalanceLogTypeConsume,
		Description: "拍照搜题服务费用",
		RelatedID:   0, // 可以关联题目ID
	}

	if err := s.balanceLogRepo.Create(balanceLog); err != nil {
		// 日志记录失败不影响主流程
		return nil
	}

	return nil
}

// GetByID 根据ID获取题目
func (s *QuestionService) GetByID(id uint) (*model.QuestionResponse, error) {
	question, err := s.questionRepo.GetByID(id)
	if err != nil {
		return nil, fmt.Errorf("查询题目失败: %w", err)
	}
	if question == nil {
		return nil, fmt.Errorf("题目不存在")
	}

	return question.ToResponse(), nil
}

// GetList 获取题目列表
func (s *QuestionService) GetList(page, pageSize int) ([]*model.QuestionResponse, int64, error) {
	if page <= 0 {
		page = 1
	}
	if pageSize <= 0 {
		pageSize = 10
	}

	offset := (page - 1) * pageSize
	questions, total, err := s.questionRepo.List(offset, pageSize)
	if err != nil {
		return nil, 0, fmt.Errorf("查询题目列表失败: %w", err)
	}

	var result []*model.QuestionResponse
	for _, question := range questions {
		result = append(result, question.ToResponse())
	}

	return result, total, nil
}

// GetBySubject 根据学科获取题目列表
func (s *QuestionService) GetBySubject(subject string, page, pageSize int) ([]*model.QuestionResponse, int64, error) {
	if page <= 0 {
		page = 1
	}
	if pageSize <= 0 {
		pageSize = 10
	}

	offset := (page - 1) * pageSize
	questions, total, err := s.questionRepo.GetBySubject(subject, offset, pageSize)
	if err != nil {
		return nil, 0, fmt.Errorf("查询题目列表失败: %w", err)
	}

	var result []*model.QuestionResponse
	for _, question := range questions {
		result = append(result, question.ToResponse())
	}

	return result, total, nil
}

// Search 搜索题目
func (s *QuestionService) SearchQuestions(keyword string, page, pageSize int) ([]*model.QuestionResponse, int64, error) {
	if page <= 0 {
		page = 1
	}
	if pageSize <= 0 {
		pageSize = 10
	}

	offset := (page - 1) * pageSize
	questions, total, err := s.questionRepo.Search(keyword, offset, pageSize)
	if err != nil {
		return nil, 0, fmt.Errorf("搜索题目失败: %w", err)
	}

	var result []*model.QuestionResponse
	for _, question := range questions {
		result = append(result, question.ToResponse())
	}

	return result, total, nil
}

// isDuplicateHashError 检查是否为重复哈希错误
func isDuplicateHashError(err error) bool {
	if err == nil {
		return false
	}
	errStr := err.Error()
	return strings.Contains(errStr, "Duplicate entry") &&
		   strings.Contains(errStr, "idx_questions_hash")
}

// GetStats 获取题目统计信息
func (s *QuestionService) GetStats() (map[string]interface{}, error) {
	// 获取总数
	totalCount, err := s.questionRepo.GetTotalCount()
	if err != nil {
		return nil, fmt.Errorf("获取题目总数失败: %w", err)
	}

	// 获取各学科统计
	subjectCount, err := s.questionRepo.GetCountBySubject()
	if err != nil {
		return nil, fmt.Errorf("获取学科统计失败: %w", err)
	}

	// 获取各年级统计
	gradeCount, err := s.questionRepo.GetCountByGrade()
	if err != nil {
		return nil, fmt.Errorf("获取年级统计失败: %w", err)
	}

	// 获取各难度统计
	difficultyCount, err := s.questionRepo.GetCountByDifficulty()
	if err != nil {
		return nil, fmt.Errorf("获取难度统计失败: %w", err)
	}

	// 获取缓存统计
	cacheStats, err := s.questionCacheRepo.GetCacheStats()
	if err != nil {
		cacheStats = map[string]interface{}{
			"redis_available": false,
			"total_keys":      0,
		}
	}

	return map[string]interface{}{
		"total_questions":   totalCount,
		"subject_count":     subjectCount,
		"grade_count":       gradeCount,
		"difficulty_count":  difficultyCount,
		"cache_stats":       cacheStats,
	}, nil
}

// ClearCache 清空题目缓存
func (s *QuestionService) ClearCache() error {
	return s.questionCacheRepo.Clear()
}

// buildAssociatedResponse 构建关联题目响应（支持S1需求3）
func (s *QuestionService) buildAssociatedResponse(questions []*model.Question, cacheHit bool, processTime int64) *model.QuestionSearchResponse {
	if len(questions) == 0 {
		return nil
	}

	// 主题目作为主要响应
	mainResponse := questions[0].ToSearchResponse(cacheHit, processTime)

	// S1需求3：添加关联题目信息
	var associatedQuestions []model.QuestionAssociation
	for i := 1; i < len(questions); i++ {
		associatedQuestions = append(associatedQuestions, questions[i].ToAssociation())
	}
	mainResponse.AssociatedQuestions = associatedQuestions

	fmt.Printf("✅ 构建关联响应完成 - 主题目ID: %d, 关联题目数量: %d\n",
		mainResponse.ID, len(associatedQuestions))

	return mainResponse
}

// CreateManagementQuestion 创建题库管理题目
func (s *QuestionService) CreateManagementQuestion(req *model.QuestionManagementRequest) (*model.Question, error) {
	return s.questionRepo.CreateManagementQuestion(req)
}

// UpdateManagementQuestion 更新题库管理题目
func (s *QuestionService) UpdateManagementQuestion(id uint, req *model.QuestionUpdateRequest) (*model.Question, error) {
	// 检查题目是否存在
	question, err := s.questionRepo.GetByID(id)
	if err != nil {
		return nil, err
	}
	if question == nil {
		return nil, fmt.Errorf("题目不存在")
	}

	// 构建更新数据
	updates := make(map[string]interface{})
	if req.QuestionType != nil {
		updates["question_type"] = *req.QuestionType
	}
	if req.QuestionDoc != nil {
		updates["question_doc"] = *req.QuestionDoc
		updates["content"] = *req.QuestionDoc // 兼容字段
		updates["question_text"] = *req.QuestionDoc // 兼容字段
	}
	if req.QuestionImg != nil {
		updates["question_img"] = *req.QuestionImg
	}
	if req.QuestionImgRaw != nil {
		updates["question_img_raw"] = *req.QuestionImgRaw
	}
	if req.OptionsA != nil {
		updates["options_a"] = *req.OptionsA
	}
	if req.OptionsB != nil {
		updates["options_b"] = *req.OptionsB
	}
	if req.OptionsC != nil {
		updates["options_c"] = *req.OptionsC
	}
	if req.OptionsD != nil {
		updates["options_d"] = *req.OptionsD
	}
	if req.OptionsY != nil {
		updates["options_y"] = *req.OptionsY
	}
	if req.OptionsN != nil {
		updates["options_n"] = *req.OptionsN
	}
	if req.Answer != nil {
		updates["answer"] = *req.Answer
	}
	if req.Analysis != nil {
		updates["analysis"] = *req.Analysis
	}
	if req.Associates != nil {
		updates["associates"] = *req.Associates
	}

	// 执行更新
	if err := s.questionRepo.UpdateQuestion(id, updates); err != nil {
		return nil, fmt.Errorf("更新题目失败: %w", err)
	}

	// 返回更新后的题目
	return s.questionRepo.GetByID(id)
}

// GetQuestionByID 根据ID获取题目（用于管理）
func (s *QuestionService) GetQuestionByID(id uint) (*model.Question, error) {
	return s.questionRepo.GetByID(id)
}

// GetManagementQuestionList 获取题库管理题目列表
func (s *QuestionService) GetManagementQuestionList(page, limit int, questionType, keyword string) ([]*model.Question, int64, error) {
	offset := (page - 1) * limit

	// 这里需要在repository中实现相应的查询方法
	// 暂时使用现有的方法，后续可以扩展
	if keyword != "" {
		questions, total, err := s.questionRepo.Search(keyword, offset, limit)
		return questions, total, err
	}

	if questionType != "" {
		// 需要在repository中添加按类型查询的方法
		questions, total, err := s.questionRepo.List(offset, limit)
		return questions, total, err
	}

	return s.questionRepo.List(offset, limit)
}

// DeleteQuestion 删除题目
func (s *QuestionService) DeleteQuestion(id uint) error {
	// 检查题目是否存在
	question, err := s.questionRepo.GetByID(id)
	if err != nil {
		return err
	}
	if question == nil {
		return fmt.Errorf("题目不存在")
	}

	// 删除题目（软删除）
	return s.questionRepo.Delete(id)
}
