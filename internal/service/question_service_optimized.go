package service

import (
	"fmt"
	"solve_api/internal/model"
	"time"
)

// OptimizedQuestionService 优化后的题目服务
type OptimizedQuestionService struct {
	questionRepo      *repository.QuestionRepository
	questionCacheRepo *repository.QuestionCacheRepository
	aiService         *AIService
}

// NewOptimizedQuestionService 创建优化后的题目服务实例
func NewOptimizedQuestionService(
	questionRepo *repository.QuestionRepository,
	questionCacheRepo *repository.QuestionCacheRepository,
	aiService *AIService,
) *OptimizedQuestionService {
	return &OptimizedQuestionService{
		questionRepo:      questionRepo,
		questionCacheRepo: questionCacheRepo,
		aiService:         aiService,
	}
}

// SearchOptimized 优化后的搜题流程 - 移除冗余逻辑
func (s *OptimizedQuestionService) SearchOptimized(userID uint, req *model.QuestionSearchRequest) (*model.QuestionSearchResponse, error) {
	startTime := time.Now()
	fmt.Printf("🚀 [优化搜题] 开始搜题流程 - 用户ID: %d\n", userID)

	// 1. 图片URL验证
	if err := s.aiService.ValidateImageURL(req.ImageURL); err != nil {
		return nil, fmt.Errorf("图片URL验证失败: %w", err)
	}

	// 2. 调用Qwen-VL获取题目结构
	qwenResult, err := s.aiService.CallQwenVL(req.ImageURL)
	if err != nil {
		return nil, fmt.Errorf("图像识别失败: %w", err)
	}

	// 3. 预处理Qwen结果（简化版本）
	preprocessed, err := s.preprocessQwenResultOptimized(qwenResult)
	if err != nil {
		return nil, fmt.Errorf("预处理失败: %w", err)
	}

	// 4. 生成缓存键
	cacheKey := s.generateOptimizedCacheKey(preprocessed)

	// 5. 查找缓存（Redis -> MySQL）
	question, cacheHit, err := s.findQuestionOptimized(cacheKey)
	if err == nil && question != nil {
		// 缓存命中，更新响应次数并返回
		s.questionRepo.IncrementResponse(question.ID)
		processTime := time.Since(startTime).Milliseconds()
		fmt.Printf("✅ [优化搜题] 缓存命中，总耗时: %v\n", time.Since(startTime))
		return question.ToSearchResponse(cacheHit, processTime), nil
	}

	// 6. 缓存未命中，调用DeepSeek
	deepseekResult, err := s.aiService.CallDeepseekWithLogID(qwenResult, qwenResult.LogID)
	if err != nil {
		return nil, fmt.Errorf("题目解析失败: %w", err)
	}

	// 7. 创建新题目记录（简化版本）
	question = s.createQuestionOptimized(preprocessed, req.ImageURL, qwenResult.RawContent, deepseekResult)

	// 8. 保存到数据库和缓存
	if err := s.saveQuestionOptimized(question); err != nil {
		return nil, fmt.Errorf("保存题目失败: %w", err)
	}

	processTime := time.Since(startTime).Milliseconds()
	fmt.Printf("✅ [优化搜题] 新题目创建完成，总耗时: %v\n", time.Since(startTime))
	return question.ToSearchResponse(false, processTime), nil
}

// preprocessQwenResultOptimized 优化后的预处理方法
func (s *OptimizedQuestionService) preprocessQwenResultOptimized(qwenResult *model.QwenVLResult) (*model.PreprocessedQuestion, error) {
	// 简化的预处理逻辑，移除冗余验证
	content := qwenResult.RawContent
	if content == "" {
		return nil, fmt.Errorf("Qwen识别内容为空")
	}

	// 解析JSON内容
	var qwenData map[string]interface{}
	if err := json.Unmarshal([]byte(content), &qwenData); err != nil {
		return nil, fmt.Errorf("解析Qwen结果失败: %w", err)
	}

	// 提取基本信息
	questionType := s.extractString(qwenData, "question_type")
	questionText := s.extractString(qwenData, "question_text")
	
	// 移除题目类型前缀
	questionText = s.removeQuestionTypePrefix(questionText)

	// 创建预处理结果
	preprocessed := &model.PreprocessedQuestion{
		QuestionType: questionType,
		QuestionText: questionText,
		Subject:      s.extractString(qwenData, "subject"),
		Grade:        s.extractString(qwenData, "grade"),
		Difficulty:   s.extractInt(qwenData, "difficulty"),
		RawContent:   content,
		IsValid:      true,
	}

	// 处理选项
	s.processOptionsOptimized(preprocessed, qwenData)

	return preprocessed, nil
}

// generateOptimizedCacheKey 优化后的缓存键生成
func (s *OptimizedQuestionService) generateOptimizedCacheKey(preprocessed *model.PreprocessedQuestion) string {
	// 基于题目类型、内容和选项生成缓存键
	content := preprocessed.QuestionType + ":" + preprocessed.QuestionText
	
	// 根据题目类型添加选项
	switch preprocessed.QuestionType {
	case model.QuestionTypeSingleChoice, model.QuestionTypeMultiChoice:
		content += "A:" + preprocessed.OptionsA + "B:" + preprocessed.OptionsB + 
				  "C:" + preprocessed.OptionsC + "D:" + preprocessed.OptionsD
	case model.QuestionTypeJudgment:
		content += "Y:" + preprocessed.OptionsY + "N:" + preprocessed.OptionsN
	}
	
	// 生成MD5哈希
	hasher := md5.New()
	hasher.Write([]byte(content))
	return hex.EncodeToString(hasher.Sum(nil))
}

// findQuestionOptimized 优化后的题目查找
func (s *OptimizedQuestionService) findQuestionOptimized(cacheKey string) (*model.Question, bool, error) {
	// 1. 先查Redis
	if question, err := s.questionCacheRepo.Get(cacheKey); err == nil && question != nil {
		return question, true, nil
	}

	// 2. 再查MySQL
	if question, err := s.questionRepo.GetByCacheKey(cacheKey); err == nil && question != nil {
		// 回写Redis
		s.questionCacheRepo.Set(cacheKey, question)
		return question, false, nil
	}

	return nil, false, fmt.Errorf("题目未找到")
}

// createQuestionOptimized 优化后的题目创建
func (s *OptimizedQuestionService) createQuestionOptimized(
	preprocessed *model.PreprocessedQuestion,
	imageURL, rawQwen string,
	deepseekResult interface{},
) *model.Question {
	question := &model.Question{
		QuestionType:   preprocessed.QuestionType,
		QuestionText:   preprocessed.QuestionText,
		QuestionImgRaw: imageURL,
		Subject:        preprocessed.Subject,
		Grade:          preprocessed.Grade,
		Difficulty:     preprocessed.Difficulty,
		Response:       0,
		RawQwen:        rawQwen,
		SourceModel:    "qwen-vl-plus,deepseek-chat",
	}

	// 设置选项
	question.OptionsA = preprocessed.OptionsA
	question.OptionsB = preprocessed.OptionsB
	question.OptionsC = preprocessed.OptionsC
	question.OptionsD = preprocessed.OptionsD
	question.OptionsY = preprocessed.OptionsY
	question.OptionsN = preprocessed.OptionsN

	// 处理DeepSeek结果
	if deepseekResult != nil {
		question.Analysis = model.ExtractStringField(deepseekResult, "Analysis")
		question.Answer = model.ExtractStringField(deepseekResult, "Answer")
		question.RawDeepseek = model.ExtractStringField(deepseekResult, "RawContent")
	}

	// 生成缓存键
	question.GenerateCacheKey()

	return question
}

// saveQuestionOptimized 优化后的题目保存
func (s *OptimizedQuestionService) saveQuestionOptimized(question *model.Question) error {
	// 1. 保存到MySQL
	if err := s.questionRepo.Create(question); err != nil {
		return err
	}

	// 2. 保存到Redis（异步，失败不影响主流程）
	go func() {
		if err := s.questionCacheRepo.Set(question.CacheKey, question); err != nil {
			fmt.Printf("⚠️ Redis缓存保存失败: %v\n", err)
		}
	}()

	return nil
}

// 辅助方法
func (s *OptimizedQuestionService) extractString(data map[string]interface{}, key string) string {
	if value, exists := data[key]; exists {
		if str, ok := value.(string); ok {
			return strings.TrimSpace(str)
		}
	}
	return ""
}

func (s *OptimizedQuestionService) extractInt(data map[string]interface{}, key string) int {
	if value, exists := data[key]; exists {
		if num, ok := value.(float64); ok {
			return int(num)
		}
	}
	return 3 // 默认难度
}

func (s *OptimizedQuestionService) removeQuestionTypePrefix(text string) string {
	// 简化的前缀移除逻辑
	for _, prefix := range model.QuestionTypePrefixes {
		if strings.HasPrefix(text, prefix) {
			return strings.TrimSpace(strings.TrimPrefix(text, prefix))
		}
	}
	return text
}

func (s *OptimizedQuestionService) processOptionsOptimized(preprocessed *model.PreprocessedQuestion, data map[string]interface{}) {
	// 简化的选项处理逻辑
	preprocessed.OptionsA = s.extractString(data, "A")
	preprocessed.OptionsB = s.extractString(data, "B")
	preprocessed.OptionsC = s.extractString(data, "C")
	preprocessed.OptionsD = s.extractString(data, "D")
	preprocessed.OptionsY = s.extractString(data, "Y")
	preprocessed.OptionsN = s.extractString(data, "N")
}
