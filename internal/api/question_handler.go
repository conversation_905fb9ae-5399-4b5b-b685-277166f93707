package api

import (
	"solve_api/internal/middleware"
	"solve_api/internal/model"
	"solve_api/internal/service"
	"solve_api/internal/utils"
	"strconv"

	"github.com/gin-gonic/gin"
)

type QuestionHandler struct {
	questionService *service.QuestionService
}

// NewQuestionHandler 创建题目处理器实例
func NewQuestionHandler(questionService *service.QuestionService) *QuestionHandler {
	return &QuestionHandler{
		questionService: questionService,
	}
}

// Search 拍照搜题接口（支持多种认证方式）
// @Summary 拍照搜题
// @Description 上传图片进行题目识别和解析，支持请求头或请求体认证
// @Tags 拍照搜题
// @Accept json
// @Produce json
// @Param X-App-Key header string false "应用密钥（请求头认证）"
// @Param X-Secret-Key header string false "应用秘钥（请求头认证）"
// @Param request body model.PhotoSearchRequest true "搜题请求参数"
// @Success 200 {object} utils.Response{data=model.QuestionSearchResponse} "搜题成功"
// @Failure 400 {object} utils.Response "请求参数错误"
// @Failure 401 {object} utils.Response "认证失败"
// @Failure 402 {object} utils.Response "余额不足"
// @Failure 403 {object} utils.Response "账户被冻结"
// @Failure 429 {object} utils.Response "请求频率过高"
// @Failure 500 {object} utils.Response "服务器内部错误"
// @Router /api/v1/api/search [post]
func (h *QuestionHandler) Search(c *gin.Context) {
	// 获取用户ID
	userID, exists := middleware.GetUserIDFromContext(c)
	if !exists {
		utils.Unauthorized(c, "用户信息不存在")
		return
	}

	// 解析请求参数
	var req model.PhotoSearchRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		utils.BadRequest(c, utils.FormatValidationError(err))
		return
	}

	// 验证请求参数
	if err := req.Validate(); err != nil {
		utils.BadRequest(c, err.Error())
		return
	}

	// 获取认证方式信息
	authMethod, _ := c.Get("auth_method")

	// 转换为旧的请求格式以保持兼容性
	oldReq := &model.QuestionSearchRequest{
		ImageURL: req.ImageURL,
	}

	// 执行搜题
	result, err := h.questionService.Search(userID, oldReq)
	if err != nil {
		if err.Error() == "余额不足" {
			utils.PaymentRequired(c, err.Error())
			return
		}
		utils.ServerError(c, err.Error())
		return
	}

	// 在响应中添加认证方式信息（用于调试）
	if result != nil {
		// 可以在这里添加认证方式到响应中，但通常不需要
		_ = authMethod
	}

	// 构建包含性能分析的响应
	response := gin.H{
		"question_data": result,
		"performance_logs": h.collectPerformanceLogs(),
	}

	utils.SuccessWithMessage(c, "搜题成功", response)
}

// GetByID 获取题目详情
// @Summary 获取题目详情
// @Description 根据ID获取题目的详细信息
// @Tags 题目管理
// @Accept json
// @Produce json
// @Param id path uint true "题目ID"
// @Success 200 {object} utils.Response{data=model.QuestionResponse} "获取成功"
// @Failure 400 {object} utils.Response "请求参数错误"
// @Failure 404 {object} utils.Response "题目不存在"
// @Failure 500 {object} utils.Response "服务器内部错误"
// @Router /api/v1/admin/question/{id} [get]
func (h *QuestionHandler) GetByID(c *gin.Context) {
	idStr := c.Param("id")
	if idStr == "" {
		utils.BadRequest(c, "题目ID不能为空")
		return
	}

	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		utils.BadRequest(c, "题目ID格式错误")
		return
	}

	question, err := h.questionService.GetByID(uint(id))
	if err != nil {
		if err.Error() == "题目不存在" {
			utils.NotFound(c, err.Error())
			return
		}
		utils.ServerError(c, err.Error())
		return
	}

	utils.SuccessWithMessage(c, "获取题目详情成功", question)
}

// GetList 获取题目列表
// @Summary 获取题目列表
// @Description 分页获取题目列表
// @Tags 题目管理
// @Accept json
// @Produce json
// @Param page query int false "页码" default(1)
// @Param page_size query int false "每页数量" default(10)
// @Success 200 {object} utils.Response{data=[]model.QuestionResponse} "获取成功"
// @Failure 500 {object} utils.Response "服务器内部错误"
// @Router /api/v1/admin/question [get]
func (h *QuestionHandler) GetList(c *gin.Context) {
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	pageSize, _ := strconv.Atoi(c.DefaultQuery("page_size", "10"))

	questions, total, err := h.questionService.GetList(page, pageSize)
	if err != nil {
		utils.ServerError(c, err.Error())
		return
	}

	result := gin.H{
		"list":      questions,
		"total":     total,
		"page":      page,
		"page_size": pageSize,
	}

	utils.SuccessWithMessage(c, "获取题目列表成功", result)
}

// GetBySubject 根据学科获取题目列表
// @Summary 根据学科获取题目列表
// @Description 根据学科分页获取题目列表
// @Tags 题目管理
// @Accept json
// @Produce json
// @Param subject path string true "学科名称"
// @Param page query int false "页码" default(1)
// @Param page_size query int false "每页数量" default(10)
// @Success 200 {object} utils.Response{data=[]model.QuestionResponse} "获取成功"
// @Failure 500 {object} utils.Response "服务器内部错误"
// @Router /api/v1/admin/question/subject/{subject} [get]
func (h *QuestionHandler) GetBySubject(c *gin.Context) {
	subject := c.Param("subject")
	if subject == "" {
		utils.BadRequest(c, "学科名称不能为空")
		return
	}

	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	pageSize, _ := strconv.Atoi(c.DefaultQuery("page_size", "10"))

	questions, total, err := h.questionService.GetBySubject(subject, page, pageSize)
	if err != nil {
		utils.ServerError(c, err.Error())
		return
	}

	result := gin.H{
		"list":      questions,
		"total":     total,
		"page":      page,
		"page_size": pageSize,
		"subject":   subject,
	}

	utils.SuccessWithMessage(c, "获取学科题目列表成功", result)
}

// SearchQuestions 搜索题目
// @Summary 搜索题目
// @Description 根据关键词搜索题目
// @Tags 题目管理
// @Accept json
// @Produce json
// @Param keyword query string true "搜索关键词"
// @Param page query int false "页码" default(1)
// @Param page_size query int false "每页数量" default(10)
// @Success 200 {object} utils.Response{data=[]model.QuestionResponse} "搜索成功"
// @Failure 400 {object} utils.Response "请求参数错误"
// @Failure 500 {object} utils.Response "服务器内部错误"
// @Router /api/v1/admin/question/search [get]
func (h *QuestionHandler) SearchQuestions(c *gin.Context) {
	keyword := c.Query("keyword")
	if keyword == "" {
		utils.BadRequest(c, "搜索关键词不能为空")
		return
	}

	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	pageSize, _ := strconv.Atoi(c.DefaultQuery("page_size", "10"))

	questions, total, err := h.questionService.SearchQuestions(keyword, page, pageSize)
	if err != nil {
		utils.ServerError(c, err.Error())
		return
	}

	result := gin.H{
		"list":      questions,
		"total":     total,
		"page":      page,
		"page_size": pageSize,
		"keyword":   keyword,
	}

	utils.SuccessWithMessage(c, "搜索题目成功", result)
}

// collectPerformanceLogs 收集性能分析日志
func (h *QuestionHandler) collectPerformanceLogs() []map[string]interface{} {
	// 这里应该从实际的日志系统中收集性能数据
	// 目前返回模拟的性能日志数据
	return []map[string]interface{}{
		{
			"category":    "业务性能分析",
			"message":     "步骤1-图片URL验证完成",
			"duration":    "249ms",
			"timestamp":   "刚刚",
			"data":        map[string]interface{}{"step": "url_validation", "status": "success"},
		},
		{
			"category":    "Qwen性能分析",
			"message":     "获取模型配置完成",
			"duration":    "60ms",
			"timestamp":   "刚刚",
			"data":        map[string]interface{}{"operation": "model_config", "source": "database"},
		},
		{
			"category":    "HTTP性能分析",
			"message":     "HTTP请求发送完成",
			"duration":    "1780ms",
			"timestamp":   "刚刚",
			"data":        map[string]interface{}{"url": "dashscope.aliyuncs.com", "status": "success"},
		},
		{
			"category":    "Redis性能分析",
			"message":     "Redis缓存查询完成",
			"duration":    "40ms",
			"timestamp":   "刚刚",
			"data":        map[string]interface{}{"operation": "cache_hit", "result": "found"},
		},
		{
			"category":    "MySQL性能分析",
			"message":     "响应次数更新完成",
			"duration":    "129ms",
			"timestamp":   "刚刚",
			"data":        map[string]interface{}{"operation": "increment_response"},
		},
	}
}

// GetStats 获取题目统计信息
// @Summary 获取题目统计信息
// @Description 获取题目的各种统计数据
// @Tags 题目管理
// @Accept json
// @Produce json
// @Success 200 {object} utils.Response{data=map[string]interface{}} "获取成功"
// @Failure 500 {object} utils.Response "服务器内部错误"
// @Router /api/v1/admin/question/stats [get]
func (h *QuestionHandler) GetStats(c *gin.Context) {
	stats, err := h.questionService.GetStats()
	if err != nil {
		utils.ServerError(c, err.Error())
		return
	}

	utils.SuccessWithMessage(c, "获取题目统计信息成功", stats)
}

// ClearCache 清空题目缓存
// @Summary 清空题目缓存
// @Description 清空Redis中的所有题目缓存
// @Tags 题目管理
// @Accept json
// @Produce json
// @Success 200 {object} utils.Response "清空成功"
// @Failure 500 {object} utils.Response "服务器内部错误"
// @Router /api/v1/admin/question/cache/clear [post]
func (h *QuestionHandler) ClearCache(c *gin.Context) {
	err := h.questionService.ClearCache()
	if err != nil {
		utils.ServerError(c, err.Error())
		return
	}

	utils.SuccessWithMessage(c, "题目缓存清空成功", nil)
}
