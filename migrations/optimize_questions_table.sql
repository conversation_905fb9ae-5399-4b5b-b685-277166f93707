-- 优化questions表结构 - 移除冗余字段，保留分散选项存储
-- 执行时间: 2024-01-01
-- 说明: 移除冗余字段，统一使用question_text作为主要内容字段，保留分散的选项字段便于维护

-- 1. 备份现有数据
CREATE TABLE IF NOT EXISTS `questions_backup_optimize` AS SELECT * FROM `questions`;

-- 2. 数据迁移：统一内容字段
-- 优先使用question_text，回退到question_doc，最后回退到content
UPDATE questions
SET question_text = CASE
    WHEN question_text IS NOT NULL AND question_text != '' THEN question_text
    WHEN question_doc IS NOT NULL AND question_doc != '' THEN question_doc
    WHEN content IS NOT NULL AND content != '' THEN content
    ELSE ''
END
WHERE question_text IS NULL OR question_text = '';

-- 3. 数据迁移：统一缓存键
-- 优先使用cache_key，回退到hash
UPDATE questions
SET cache_key = CASE
    WHEN cache_key IS NOT NULL AND cache_key != '' THEN cache_key
    WHEN hash IS NOT NULL AND hash != '' THEN hash
    ELSE MD5(CONCAT(COALESCE(question_type, ''), ':', COALESCE(question_text, '')))
END
WHERE cache_key IS NULL OR cache_key = '';

-- 4. 数据迁移：确保分散选项字段的数据完整性
-- 如果JSON options字段有数据但分散字段为空，则从JSON中提取
UPDATE questions
SET
    options_a = COALESCE(NULLIF(options_a, ''), JSON_UNQUOTE(JSON_EXTRACT(options, '$.A')), ''),
    options_b = COALESCE(NULLIF(options_b, ''), JSON_UNQUOTE(JSON_EXTRACT(options, '$.B')), ''),
    options_c = COALESCE(NULLIF(options_c, ''), JSON_UNQUOTE(JSON_EXTRACT(options, '$.C')), ''),
    options_d = COALESCE(NULLIF(options_d, ''), JSON_UNQUOTE(JSON_EXTRACT(options, '$.D')), ''),
    options_y = COALESCE(NULLIF(options_y, ''), JSON_UNQUOTE(JSON_EXTRACT(options, '$.Y')), ''),
    options_n = COALESCE(NULLIF(options_n, ''), JSON_UNQUOTE(JSON_EXTRACT(options, '$.N')), '')
WHERE options IS NOT NULL AND options != '' AND JSON_VALID(options);

-- 5. 验证数据迁移
SELECT
    COUNT(*) as total_records,
    COUNT(CASE WHEN question_text IS NOT NULL AND question_text != '' THEN 1 END) as with_question_text,
    COUNT(CASE WHEN cache_key IS NOT NULL AND cache_key != '' THEN 1 END) as with_cache_key,
    COUNT(CASE WHEN options_a != '' OR options_b != '' OR options_c != '' OR options_d != '' THEN 1 END) as with_choice_options,
    COUNT(CASE WHEN options_y != '' OR options_n != '' THEN 1 END) as with_judgment_options
FROM questions;

-- 6. 删除冗余字段（分步执行，确保数据安全）
-- 第一批：明确的冗余内容字段
ALTER TABLE questions
DROP COLUMN content,           -- 与question_text重复
DROP COLUMN question_doc,      -- 与question_text重复
DROP COLUMN hash,              -- 与cache_key重复
DROP COLUMN raw_content;       -- 与raw_qwen重复

-- 第二批：JSON选项字段（保留分散字段，删除JSON字段）
ALTER TABLE questions
DROP COLUMN options;           -- 删除JSON格式选项，保留分散字段

-- 第三批：很少使用的字段
ALTER TABLE questions
DROP COLUMN question_img,      -- 很少使用
DROP COLUMN associates;        -- 关联逻辑复杂，很少使用

-- 7. 优化索引
-- 删除旧索引
DROP INDEX IF EXISTS idx_questions_hash ON questions;
DROP INDEX IF EXISTS idx_questions_question_type_new ON questions;
DROP INDEX IF EXISTS idx_questions_associates ON questions;

-- 创建新的优化索引
CREATE UNIQUE INDEX idx_questions_cache_key_unique ON questions(cache_key);
CREATE INDEX idx_questions_question_type ON questions(question_type);
CREATE INDEX idx_questions_subject_grade ON questions(subject, grade);
CREATE INDEX idx_questions_difficulty ON questions(difficulty);
CREATE INDEX idx_questions_response ON questions(response);
CREATE INDEX idx_questions_created_at ON questions(created_at);

-- 8. 添加字段约束和优化
ALTER TABLE questions
MODIFY COLUMN cache_key VARCHAR(128) NOT NULL COMMENT '缓存键，基于题目内容生成',
MODIFY COLUMN question_type VARCHAR(20) NOT NULL COMMENT '题目类型',
MODIFY COLUMN question_text TEXT NOT NULL COMMENT '题目内容',
MODIFY COLUMN options_a TEXT COMMENT '选项A',
MODIFY COLUMN options_b TEXT COMMENT '选项B',
MODIFY COLUMN options_c TEXT COMMENT '选项C',
MODIFY COLUMN options_d TEXT COMMENT '选项D',
MODIFY COLUMN options_y TEXT COMMENT '选项Y（正确）',
MODIFY COLUMN options_n TEXT COMMENT '选项N（错误）',
MODIFY COLUMN answer TEXT NOT NULL COMMENT '正确答案',
MODIFY COLUMN analysis TEXT COMMENT '答案解析',
MODIFY COLUMN response INT DEFAULT 0 NOT NULL COMMENT '响应次数',
MODIFY COLUMN subject VARCHAR(20) DEFAULT '未知' COMMENT '学科',
MODIFY COLUMN grade VARCHAR(20) DEFAULT '未知' COMMENT '年级',
MODIFY COLUMN difficulty INT DEFAULT 3 COMMENT '难度 1-5',
MODIFY COLUMN source_model VARCHAR(50) DEFAULT 'unknown' COMMENT '来源模型';

-- 9. 更新表注释
ALTER TABLE questions COMMENT = '题库表 - 优化版本，移除冗余字段，保留分散选项存储便于维护';

-- 10. 最终验证
SELECT
    COLUMN_NAME,
    DATA_TYPE,
    IS_NULLABLE,
    COLUMN_DEFAULT,
    COLUMN_COMMENT
FROM INFORMATION_SCHEMA.COLUMNS
WHERE TABLE_SCHEMA = DATABASE()
AND TABLE_NAME = 'questions'
ORDER BY ORDINAL_POSITION;
