deepseek返回的样本示例；


{
  "question_type": "多选题",
  "question_text": "同车道行驶的机动车,后车应当与前车保持足以采取紧急制动措施的安全距离。下列哪种情形不得超车？",
  "options": {
    "A": "前车正在左转弯的",
    "B": "前车正在上下乘客的",
    "C": "前车正在超车的",
    "D": "前车正在掉头的"
  },
  "answer": {
    "A": "前车正在左转弯的",
    "C": "前车正在超车的",
    "D": "前车正在掉头的"
  },
  "analysis": "根据《道路交通安全法》的相关规定，同车道行驶的机动车，后车应当与前车保持足以采取紧急制动措施的安全距离。在以下几种情形下，后车不得超车：1.前车正在左转弯的；2.前车正在超车的；3.前车正在掉头的。这些情况下超车会增加交通事故的风险，因此法律明确禁止。选项B“前车正在上下乘客的”虽然也是需要注意的情况，但并不直接属于法律规定的不得超车的情形。"
}

{
  "question_type": "单选题",
  "question_text": "如图所示，驾驶机动车遇到这种情形时，以下做法正确的是什么？",
  "options": {
    "A": "减速靠右，待对向车辆通过后，再缓慢超越行人",
    "B": "鸣喇叭提示左侧车辆后，保持原速行驶",
    "C": "加速行驶，在对面来车交会前超过行人",
    "D": "鸣喇叭提示行人后，保持原速行驶"
  },
  "answer": {
    "A": "减速靠右，待对向车辆通过后，再缓慢超越行人"
  },
  "analysis": "在驾驶机动车遇到对向有来车且前方有行人横穿马路的情形时，最安全的做法是减速靠右，等待对向车辆通过后，再缓慢超越行人。这样可以避免与对向车辆发生碰撞，同时确保行人的安全。鸣喇叭或保持原速行驶可能会对行人造成惊吓或无法及时避让，加速行驶则增加了与对向车辆或行人发生碰撞的风险。因此，选项A是正确的做法。"
}


{
  "question_type": "判断题",
  "question_text": "一个周期内记分满12分的驾驶人,拒不参加学习也不接受考试的,公安机关交通管理部门将公告其驾驶证停止使用。",
  "options": {
    "Y": "正确",
    "N": "错误"
  },
  "answer": {
    "Y": "正确"
  },
  "analysis": "根据《道路交通安全法》及相关规定，驾驶人在一个记分周期内累积记分达到12分的，应当在规定时间内到公安机关交通管理部门接受教育和考试。如果驾驶人拒不参加学习也不接受考试，公安机关交通管理部门有权公告其驾驶证停止使用。因此，题目描述的情况是正确的。"
}


分析样本，完善处理在收到deepseek的响应数据后的数据入库以及用户响应问题：
1. 调整数据表的options字段，拆分为options_a, options_b, options_c, options_d, options_y, options_n 6个字段，
2. 识别出题目的类型，根据题目类型，将选项分别存储到对应的字段中，其中单选题和多选题存储到options_a, options_b, options_c, options_d四个字段中，判断题存储到options_y和options_n两个字段中

3. 用户请求的响应除了deepseek返回的字段外，还需要将question_img、question_img_raw、以及如果associates关联键字段存在其他题的id，则将关联的题的信息，一同返回。

